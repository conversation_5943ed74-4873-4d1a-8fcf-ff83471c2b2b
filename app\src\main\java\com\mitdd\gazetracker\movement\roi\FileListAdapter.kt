package com.mitdd.gazetracker.movement.roi

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R

/**
 * FileName: FileListAdapter
 * Author by AI Assistant, Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 文件列表适配器
 */
class FileListAdapter(
    private val fileList: List<CustomFilePickerActivity.FileItem>,
    private val onItemClick: (CustomFilePickerActivity.FileItem) -> Unit
) : RecyclerView.Adapter<FileListAdapter.FileViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FileViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_file_picker, parent, false)
        return FileViewHolder(view)
    }

    override fun onBindViewHolder(holder: FileViewHolder, position: Int) {
        val fileItem = fileList[position]
        holder.bind(fileItem, onItemClick)
    }

    override fun getItemCount(): Int = fileList.size

    class FileViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivFileIcon: ImageView = itemView.findViewById(R.id.iv_file_icon)
        private val tvFileName: TextView = itemView.findViewById(R.id.tv_file_name)

        fun bind(
            fileItem: CustomFilePickerActivity.FileItem,
            onItemClick: (CustomFilePickerActivity.FileItem) -> Unit
        ) {
            tvFileName.text = fileItem.name

            // 设置图标
            when {
                fileItem.isParentDir -> {
                    ivFileIcon.setImageResource(R.drawable.ic_folder_up)
                }
                fileItem.isDirectory -> {
                    ivFileIcon.setImageResource(R.drawable.ic_folder)
                }
                else -> {
                    ivFileIcon.setImageResource(R.drawable.ic_image)
                }
            }

            // 设置点击事件
            itemView.setOnSingleClickListener {
                onItemClick(fileItem)
            }
        }
    }
}
