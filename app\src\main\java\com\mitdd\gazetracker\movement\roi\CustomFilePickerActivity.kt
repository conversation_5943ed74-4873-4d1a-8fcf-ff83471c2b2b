package com.mitdd.gazetracker.movement.roi

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Environment
import android.widget.Toast
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import com.airdoc.component.common.log.Logger
import android.widget.TextView
import android.widget.ImageView
import java.io.File

/**
 * FileName: CustomFilePickerActivity
 * Author by AI Assistant, Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 自定义文件选择器Activity，支持浏览指定目录并选择图片文件
 */
class CustomFilePickerActivity : GTBaseActivity() {

    companion object {
        private val TAG = CustomFilePickerActivity::class.java.simpleName
        
        const val EXTRA_INITIAL_PATH = "extra_initial_path"
        const val EXTRA_SELECTED_FILE_PATH = "extra_selected_file_path"
        
        // 支持的图片格式
        private val SUPPORTED_IMAGE_EXTENSIONS = setOf(
            "jpg", "jpeg", "png", "bmp", "gif", "webp"
        )
        
        fun createIntent(context: Context, initialPath: String = "/sdcard"): Intent {
            return Intent(context, CustomFilePickerActivity::class.java).apply {
                putExtra(EXTRA_INITIAL_PATH, initialPath)
            }
        }
    }

    private val tvCurrentPath by id<TextView>(R.id.tv_current_path)
    private val ivBack by id<ImageView>(R.id.iv_back)
    private val rvFileList by id<RecyclerView>(R.id.rv_file_list)
    
    private lateinit var fileAdapter: FileListAdapter
    private var currentPath: String = "/sdcard"
    private val fileList = mutableListOf<FileItem>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_custom_file_picker)
        
        initParam()
        initView()
        initListener()
        loadDirectory(currentPath)
    }

    private fun initParam() {
        currentPath = intent.getStringExtra(EXTRA_INITIAL_PATH) ?: "/sdcard"
        Logger.d(TAG, msg = "初始路径: $currentPath")
    }

    private fun initView() {
        tvCurrentPath.text = currentPath
        
        fileAdapter = FileListAdapter(fileList) { fileItem ->
            onFileItemClick(fileItem)
        }
        
        rvFileList.layoutManager = LinearLayoutManager(this)
        rvFileList.adapter = fileAdapter
    }

    private fun initListener() {
        ivBack.setOnSingleClickListener {
            onBackPressed()
        }
    }

    private fun loadDirectory(path: String) {
        Logger.d(TAG, msg = "加载目录: $path")
        
        try {
            val directory = File(path)
            if (!directory.exists() || !directory.isDirectory) {
                Toast.makeText(this, "目录不存在或无法访问", Toast.LENGTH_SHORT).show()
                return
            }

            fileList.clear()
            
            // 添加返回上级目录选项（除非已经在根目录）
            if (path != "/" && path != "/sdcard") {
                fileList.add(FileItem(
                    name = "..",
                    path = directory.parent ?: "/sdcard",
                    isDirectory = true,
                    isParentDir = true
                ))
            }

            // 获取目录下的所有文件和文件夹
            val files = directory.listFiles()
            if (files != null) {
                // 先添加文件夹，再添加文件
                val directories = files.filter { it.isDirectory }.sortedBy { it.name.lowercase() }
                val imageFiles = files.filter { it.isFile && isImageFile(it) }.sortedBy { it.name.lowercase() }
                
                directories.forEach { file ->
                    fileList.add(FileItem(
                        name = file.name,
                        path = file.absolutePath,
                        isDirectory = true,
                        isParentDir = false
                    ))
                }
                
                imageFiles.forEach { file ->
                    fileList.add(FileItem(
                        name = file.name,
                        path = file.absolutePath,
                        isDirectory = false,
                        isParentDir = false
                    ))
                }
            }

            currentPath = path
            tvCurrentPath.text = currentPath
            fileAdapter.notifyDataSetChanged()
            
            Logger.d(TAG, msg = "目录加载完成，共 ${fileList.size} 个项目")
            
        } catch (e: Exception) {
            Logger.e(TAG, msg = "加载目录失败: ${e.message}")
            Toast.makeText(this, "加载目录失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun onFileItemClick(fileItem: FileItem) {
        if (fileItem.isDirectory) {
            // 点击文件夹，进入该文件夹
            loadDirectory(fileItem.path)
        } else {
            // 点击图片文件，选择该文件
            Logger.d(TAG, msg = "选择图片文件: ${fileItem.path}")
            val resultIntent = Intent().apply {
                putExtra(EXTRA_SELECTED_FILE_PATH, fileItem.path)
            }
            setResult(Activity.RESULT_OK, resultIntent)
            finish()
        }
    }

    private fun isImageFile(file: File): Boolean {
        val extension = file.extension.lowercase()
        return SUPPORTED_IMAGE_EXTENSIONS.contains(extension)
    }

    override fun onBackPressed() {
        if (currentPath != "/sdcard" && currentPath != "/") {
            // 返回上级目录
            val parentFile = File(currentPath).parentFile
            if (parentFile != null && parentFile.exists()) {
                loadDirectory(parentFile.absolutePath)
            } else {
                super.onBackPressed()
            }
        } else {
            super.onBackPressed()
        }
    }

    /**
     * 文件项数据类
     */
    data class FileItem(
        val name: String,
        val path: String,
        val isDirectory: Boolean,
        val isParentDir: Boolean = false
    )
}
