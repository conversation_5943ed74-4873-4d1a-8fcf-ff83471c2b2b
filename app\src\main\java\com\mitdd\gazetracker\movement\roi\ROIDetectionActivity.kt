package com.mitdd.gazetracker.movement.roi

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Path
import android.os.Bundle
import android.view.Gravity
import android.widget.ImageView
import android.widget.TextView
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.bumptech.glide.Glide
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.luck.picture.lib.entity.LocalMedia
import com.luck.picture.lib.interfaces.OnResultCallbackListener
import com.luck.picture.lib.utils.MediaUtils
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.base.GTBaseActivity
import androidx.core.net.toUri
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.isVisible
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.jeremyliao.liveeventbus.LiveEventBus

/**
 * FileName: ROIDetectionActivity
 * Author by lilin,Date on 2025/5/14 15:34
 * PS: Not easy to write code, please indicate.
 * 兴趣区域检测
 */
class ROIDetectionActivity : GTBaseActivity() {

    companion object{
        private val TAG = ROIDetectionActivity::class.java.simpleName

        const val OUTPUT_PARAM_PICTURE_BITMAP = "PictureBitmap"
        const val OUTPUT_PARAM_ROI_PATH = "ROIPath"

        fun createIntent(context: Context): Intent {
            return Intent(context, ROIDetectionActivity::class.java)
        }
    }

    private val ivPicture by id<ImageView>(R.id.iv_picture)
    private val roiPathView by id<ROIPathView>(R.id.roi_path_view)
    private val ivSettings by id<ImageView>(R.id.iv_settings)
    private val tvClearRoi by id<TextView>(R.id.tv_clear_roi)
    private val tvSaveRoi by id<TextView>(R.id.tv_save_roi)

    private var mPictureBitmap:Bitmap? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_roi_detection)

        initView()
        initListener()
    }

    private fun initView() {

    }

    private fun initListener(){
        ivSettings.setOnSingleClickListener {
            val roiDetectionMenu = ROIDetectionMenu(this)
            roiDetectionMenu.showAsDropDown(ivSettings,0, 10.dp2px(this), Gravity.END)
            roiDetectionMenu.onSelectPictureClick = {
                selectPicture()
                roiPathView.clearCanvas()
                roiPathView.setMarking(false)
                tvClearRoi.isVisible = false
                tvSaveRoi.isVisible = false
            }
            roiDetectionMenu.onMarkRoiClick = {
                roiPathView.setMarking(true)
                ivSettings.isVisible = false
                tvClearRoi.isVisible = true
                tvSaveRoi.isVisible = true
            }
            roiDetectionMenu.onStartDetectionClick = {
                LiveEventBus.get<Bitmap?>(OUTPUT_PARAM_PICTURE_BITMAP).post(mPictureBitmap)
                LiveEventBus.get<List<Path>>(OUTPUT_PARAM_ROI_PATH).post(roiPathView.getPaths())
                startActivity(ROIDetectingActivity.createIntent(this))
                finish()
            }
        }
        tvClearRoi.setOnSingleClickListener {
            roiPathView.clearCanvas()
        }
        tvSaveRoi.setOnSingleClickListener {
            roiPathView.setMarking(false)
            ivSettings.isVisible = true
            tvClearRoi.isVisible = false
            tvSaveRoi.isVisible = false
        }
    }

    private fun selectPicture(){
        PictureSelector.create(this)
            .openGallery(SelectMimeType.ofImage())
            .setImageEngine(GlideEngine)//设置相册图片加载引擎
            .setSelectionMode(SelectModeConfig.SINGLE)
            .isDisplayCamera(false)
            .forResult(object :OnResultCallbackListener<LocalMedia>{
                override fun onResult(result: ArrayList<LocalMedia>?) {
                    if (!result.isNullOrEmpty()){
                        val media = result[0]
                        if (PictureMimeType.isHasImage(media.mimeType)){
                            val imageExtraInfo =
                                MediaUtils.getImageSize(this@ROIDetectionActivity, media.path)
                            media.width = imageExtraInfo.width
                            media.height = imageExtraInfo.height
                            Glide.with(this@ROIDetectionActivity)
                                .asBitmap()
                                .load(
                                    media.path.takeIf { PictureMimeType.isContent(it) && !media.isCut && !media.isCompressed }?.toUri() ?: media.path
                                )
                                .diskCacheStrategy(DiskCacheStrategy.ALL)
                                .listener(object : RequestListener<Bitmap> {

                                    override fun onResourceReady(
                                        resource: Bitmap?,
                                        model: Any?,
                                        target: Target<Bitmap>?,
                                        dataSource: DataSource?,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        mPictureBitmap = resource
                                        return false // 返回 false，表示未处理，继续让 Glide 处理
                                    }

                                    override fun onLoadFailed(
                                        e: GlideException?,
                                        model: Any?,
                                        target: Target<Bitmap>?,
                                        isFirstResource: Boolean
                                    ): Boolean {
                                        mPictureBitmap = null
                                        return false // 返回 false，表示未处理，继续让 Glide 处理
                                    }
                                })
                                .into(ivPicture)
                        }
                    }
                }

                override fun onCancel() {
                }

            })
    }

}